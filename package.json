{"name": "setmee-landing", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "perf-check": "node scripts/performance-check.js", "build:analyze": "ANALYZE=true npm run build", "test:build": "npm run build && npm run perf-check", "clean": "rm -rf .next out dist", "prepare": "npm run lint && npm run type-check"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "prettier": "^3.6.2", "typescript": "^5"}}