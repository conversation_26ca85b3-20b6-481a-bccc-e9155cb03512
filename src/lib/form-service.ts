import { ContactFormData, EmailSubscriptionData } from './validations';
import { FormType, getFormConfig, isWebhookConfigured } from '@/config/forms';

// Интерфейс для ответа от сервиса форм
export interface FormSubmissionResult {
  success: boolean;
  message: string;
}

// Интерфейс для данных, отправляемых на Make.com webhook
interface WebhookPayload {
  formType: string;
  timestamp: string;
  source: string;
  [key: string]: string | number | boolean | undefined;
}

// Универсальная функция для отправки данных на Make.com webhook
export const submitForm = async (
  formType: FormType,
  formData: Record<string, string | number | boolean | undefined>
): Promise<FormSubmissionResult> => {
  const config = getFormConfig(formType);

  try {
    // Проверяем, настроен ли webhook
    if (!isWebhookConfigured(formType)) {
      console.warn(`Webhook для формы ${formType} не настроен. Данные логируются в консоль.`);
      console.log(`${formType} form data:`, formData);

      return {
        success: true,
        message: config.successMessage,
      };
    }

    // Дополнительная проверка URL
    const webhookUrl = config.webhookUrl;
    if (!webhookUrl || !webhookUrl.startsWith('http')) {
      console.error(`Неправильный webhook URL для формы ${formType}:`, webhookUrl);
      console.log(`${formType} form data (fallback):`, formData);

      return {
        success: true,
        message: config.successMessage,
      };
    }

    console.log(`Отправка ${formType} формы на webhook:`, webhookUrl);

    // Подготавливаем данные для отправки
    const payload: WebhookPayload = {
      formType,
      timestamp: new Date().toISOString(),
      source: 'setmee-website',
      ...formData
    };

    console.log(`${formType} payload:`, payload);

    // Отправляем данные на webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`HTTP error! status: ${response.status}, response:`, errorText);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    console.log(`${formType} form submitted successfully to Make.com`);

    return {
      success: true,
      message: config.successMessage,
    };
  } catch (error) {
    console.error(`${formType} form submission error:`, error);
    console.log(`${formType} form data (error fallback):`, formData);

    return {
      success: false,
      message: config.errorMessage,
    };
  }
};

// Специализированные функции для каждого типа формы
export const submitContactForm = async (data: ContactFormData): Promise<FormSubmissionResult> => {
  return submitForm('contact', data);
};

export const submitEmailSubscription = async (data: EmailSubscriptionData): Promise<FormSubmissionResult> => {
  return submitForm('newsletter', data);
};

// ===== LEGACY INTEGRATIONS (для справки) =====
// Эти функции оставлены для справки, если потребуется интеграция с другими сервисами

// Example integration with Formspree (commented out)
/*
export const submitContactFormFormspree = async (data: ContactFormData) => {
  const response = await fetch('https://formspree.io/f/YOUR_FORM_ID', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error('Failed to submit form');
  }

  return response.json();
};
*/

// Example integration with EmailJS (commented out)
/*
import emailjs from '@emailjs/browser';

export const submitContactFormEmailJS = async (data: ContactFormData) => {
  const result = await emailjs.send(
    'YOUR_SERVICE_ID',
    'YOUR_TEMPLATE_ID',
    {
      from_name: data.name,
      from_email: data.email,
      phone: data.phone,
      company: data.company,
      message: data.message,
    },
    'YOUR_PUBLIC_KEY'
  );

  return result;
};
*/


