import { z } from 'zod';

export const contactFormSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters'),
  email: z
    .string()
    .email('Please enter a valid email address'),
  phone: z
    .string()
    .optional()
    .refine((val) => !val || val.length >= 10, {
      message: 'Phone number must be at least 10 digits',
    }),
  company: z
    .string()
    .optional(),
  message: z
    .string()
    .min(10, 'Message must be at least 10 characters')
    .max(1000, 'Message must be less than 1000 characters'),
});

export const emailSubscriptionSchema = z.object({
  email: z
    .string()
    .email('Please enter a valid email address'),
});

export type ContactFormData = z.infer<typeof contactFormSchema>;
export type EmailSubscriptionData = z.infer<typeof emailSubscriptionSchema>;
