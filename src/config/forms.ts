// Конфигурация форм для отправки на Make.com webhooks

export interface FormConfig {
  webhookUrl: string | undefined;
  successMessage: string;
  errorMessage: string;
  fields: string[];
  type: string;
}

export interface FormConfigs {
  contact: FormConfig;
  newsletter: FormConfig;
}

export const formConfigs: FormConfigs = {
  contact: {
    webhookUrl: process.env.NEXT_PUBLIC_MAKE_CONTACT_WEBHOOK,
    successMessage: 'Thank you for your message! We will contact you within 24 hours.',
    errorMessage: 'An error occurred while sending the message. Please try again.',
    fields: ['name', 'email', 'phone', 'company', 'message'],
    type: 'contact'
  },
  newsletter: {
    webhookUrl: process.env.NEXT_PUBLIC_MAKE_NEWSLETTER_WEBHOOK,
    successMessage: 'Thank you for subscribing! We will send you the presentation shortly.',
    errorMessage: 'An error occurred during subscription. Please try again.',
    fields: ['email'],
    type: 'newsletter'
  }
};

export type FormType = keyof FormConfigs;

// Функция для получения конфигурации формы
export const getFormConfig = (formType: FormType): FormConfig => {
  return formConfigs[formType];
};

// Функция для проверки, настроен ли webhook для формы
export const isWebhookConfigured = (formType: FormType): boolean => {
  const config = getFormConfig(formType);
  const url = config.webhookUrl;
  return !!(url && url.startsWith('http'));
};

// Функция для отладки конфигурации
export const debugFormConfig = (formType: FormType): void => {
  const config = getFormConfig(formType);
  console.log(`=== Debug ${formType} form config ===`);
  console.log('Webhook URL:', config.webhookUrl);
  console.log('Is configured:', isWebhookConfigured(formType));
  console.log('Success message:', config.successMessage);
  console.log('Error message:', config.errorMessage);
  console.log('Fields:', config.fields);
  console.log('================================');
};
