import React from 'react';
import { cn } from '@/lib/utils';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'bordered' | 'elevated' | 'flat';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  className,
  variant = 'default',
  padding = 'md',
  hover = false,
}) => {
  const variantClasses = {
    default: 'bg-white border border-gray-200 rounded-lg',
    bordered: 'bg-white border-2 border-gray-300 rounded-lg',
    elevated: 'bg-white shadow-lg rounded-lg border border-gray-100',
    flat: 'bg-gray-50 rounded-lg',
  };
  
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  };
  
  const hoverClasses = hover
    ? 'transition-all duration-200 hover:shadow-xl hover:-translate-y-1'
    : '';
  
  return (
    <div
      className={cn(
        variantClasses[variant],
        paddingClasses[padding],
        hoverClasses,
        className
      )}
    >
      {children}
    </div>
  );
};

export default Card;
