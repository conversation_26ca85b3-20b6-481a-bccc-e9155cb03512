import React from 'react';

const StructuredData: React.FC = () => {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Setmee",
    "description": "Certified Kommo CRM partner specializing in implementation, optimization, training and custom integrations",
    "url": "https://setmee.com",
    "logo": "https://setmee.com/images/setmee-logo.svg",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-123-4567",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": ["English"]
    },
    "sameAs": [
      "https://www.linkedin.com/company/setmee",
      "https://twitter.com/setmee"
    ],
    "foundingDate": "2016",
    "numberOfEmployees": "10-50",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    }
  };

  const serviceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Kommo CRM Implementation and Optimization",
    "description": "Professional Kommo CRM implementation, optimization, training and custom integration services",
    "provider": {
      "@type": "Organization",
      "name": "Setmee"
    },
    "serviceType": "CRM Implementation",
    "areaServed": "Worldwide",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Kommo CRM Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Kommo Implementation",
            "description": "Complete CRM implementation tailored to your business needs"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Optimisation of use",
            "description": "Maximize efficiency and get the most out of your Kommo system"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Customised integrations",
            "description": "Custom solutions that make Kommo even more efficient"
          }
        }
      ]
    }
  };

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Setmee",
    "url": "https://setmee.com",
    "description": "Smart solutions for your business based on Kommo CRM",
    "publisher": {
      "@type": "Organization",
      "name": "Setmee"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://setmee.com/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceSchema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema),
        }}
      />
    </>
  );
};

export default StructuredData;
