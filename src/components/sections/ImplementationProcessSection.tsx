import React from 'react';
import { <PERSON>, SectionHead<PERSON>, But<PERSON> } from '@/components/ui';
import { implementationSteps } from '@/data/content';

const ImplementationProcessSection: React.FC = () => {
  return (
    <Section
      id="implementation-process"
      padding="xl"
      background="white"
    >
      <div className="grid lg:grid-cols-2 gap-12 items-center">
        {/* Content */}
        <div className="space-y-8">
          <SectionHeader
            title="To increase efficiency, we have divided the implementation process into several stages"
            align="left"
            className="mb-8"
          />

          <div className="space-y-6">
            {implementationSteps.map((step) => (
              <div key={step.id} className="flex gap-4">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-sm">
                    {step.order}
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="pt-6">
            <Button
              href="/contact"
              variant="primary"
              size="lg"
            >
              Contact a specialist
            </Button>
          </div>
        </div>

        {/* Visual */}
        <div className="relative">
          <div className="bg-gradient-to-br from-blue-50 via-purple-50 to-orange-50 rounded-2xl p-6 border border-gray-200 shadow-lg">
            <svg viewBox="0 0 500 400" className="w-full h-96">
              {/* Main dashboard background */}
              <rect x="0" y="0" width="500" height="400" rx="12" fill="white" stroke="#e5e7eb" strokeWidth="2"/>

              {/* Header */}
              <rect x="20" y="20" width="460" height="40" rx="8" fill="#f8fafc"/>
              <text x="250" y="35" textAnchor="middle" fontSize="14" fill="#1f2937" fontWeight="bold">Kommo Implementation Dashboard</text>
              <text x="250" y="50" textAnchor="middle" fontSize="10" fill="#6b7280">Real-time Process Monitoring</text>

              {/* Timeline */}
              <line x1="50" y1="100" x2="450" y2="100" stroke="#e5e7eb" strokeWidth="3"/>

              {/* Stage 1: Audit */}
              <circle cx="100" cy="100" r="12" fill="#3b82f6"/>
              <text x="100" y="105" textAnchor="middle" fontSize="10" fill="white" fontWeight="bold">1</text>
              <rect x="60" y="120" width="80" height="60" rx="8" fill="#dbeafe" stroke="#3b82f6"/>
              <text x="100" y="135" textAnchor="middle" fontSize="8" fill="#1d4ed8" fontWeight="bold">AUDIT</text>

              {/* Audit checklist */}
              <rect x="70" y="140" width="60" height="4" rx="2" fill="#10b981"/>
              <circle cx="135" cy="142" r="2" fill="#10b981"/>
              <path d="M133.5 142l1 1 1.5-1.5" stroke="white" strokeWidth="0.5" fill="none"/>

              <rect x="70" y="148" width="50" height="4" rx="2" fill="#f59e0b"/>
              <circle cx="125" cy="150" r="2" fill="#f59e0b"/>
              <text x="125" y="151.5" textAnchor="middle" fontSize="3" fill="white">!</text>

              <rect x="70" y="156" width="45" height="4" rx="2" fill="#ef4444"/>
              <circle cx="120" cy="158" r="2" fill="#ef4444"/>
              <path d="M118.5 156.5l3 3M121.5 156.5l-3 3" stroke="white" strokeWidth="0.5"/>

              <text x="100" y="170" textAnchor="middle" fontSize="7" fill="#1d4ed8">Analysis: 70%</text>

              {/* Stage 2: Setup */}
              <circle cx="200" cy="100" r="12" fill="#8b5cf6"/>
              <text x="200" y="105" textAnchor="middle" fontSize="10" fill="white" fontWeight="bold">2</text>
              <rect x="160" y="120" width="80" height="60" rx="8" fill="#f3e8ff" stroke="#8b5cf6"/>
              <text x="200" y="135" textAnchor="middle" fontSize="8" fill="#7c3aed" fontWeight="bold">SETUP</text>

              {/* Funnel visualization */}
              <rect x="170" y="140" width="25" height="30" rx="3" fill="#8b5cf6" opacity="0.8"/>
              <rect x="172" y="142" width="21" height="4" rx="2" fill="white"/>
              <rect x="174" y="148" width="17" height="4" rx="2" fill="white"/>
              <rect x="176" y="154" width="13" height="4" rx="2" fill="white"/>
              <rect x="178" y="160" width="9" height="4" rx="2" fill="white"/>
              <rect x="180" y="166" width="5" height="3" rx="1" fill="white"/>

              {/* Integration icons */}
              <circle cx="210" cy="145" r="6" fill="#10b981"/>
              <text x="210" y="148" textAnchor="middle" fontSize="4" fill="white">API</text>
              <circle cx="225" cy="145" r="6" fill="#3b82f6"/>
              <text x="225" y="148" textAnchor="middle" fontSize="4" fill="white">WEB</text>

              <text x="200" y="170" textAnchor="middle" fontSize="7" fill="#7c3aed">Config: 85%</text>

              {/* Stage 3: Training */}
              <circle cx="300" cy="100" r="12" fill="#10b981"/>
              <text x="300" y="105" textAnchor="middle" fontSize="10" fill="white" fontWeight="bold">3</text>
              <rect x="260" y="120" width="80" height="60" rx="8" fill="#f0fdf4" stroke="#10b981"/>
              <text x="300" y="135" textAnchor="middle" fontSize="8" fill="#059669" fontWeight="bold">TRAINING</text>

              {/* Video call interface */}
              <rect x="270" y="140" width="30" height="20" rx="4" fill="#dcfce7"/>
              <circle cx="285" cy="150" r="5" fill="#10b981"/>
              <circle cx="285" cy="148" r="2" fill="white"/>
              <path d="M282 152 Q285 151 288 152" stroke="white" strokeWidth="0.8" fill="none"/>

              {/* Training materials */}
              <rect x="305" y="140" width="12" height="8" rx="2" fill="#3b82f6"/>
              <text x="311" y="145" textAnchor="middle" fontSize="3" fill="white">PDF</text>
              <rect x="320" y="140" width="12" height="8" rx="2" fill="#f59e0b"/>
              <text x="326" y="145" textAnchor="middle" fontSize="3" fill="white">VID</text>

              {/* Live indicator */}
              <circle cx="275" cy="145" r="2" fill="#ef4444">
                <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite"/>
              </circle>
              <text x="280" y="147" fontSize="4" fill="#ef4444">LIVE</text>

              <text x="300" y="170" textAnchor="middle" fontSize="7" fill="#059669">Progress: 75%</text>

              {/* Stage 4: Support */}
              <circle cx="400" cy="100" r="12" fill="#f59e0b"/>
              <text x="400" y="105" textAnchor="middle" fontSize="10" fill="white" fontWeight="bold">4</text>
              <rect x="360" y="120" width="80" height="60" rx="8" fill="#fff7ed" stroke="#f59e0b"/>
              <text x="400" y="135" textAnchor="middle" fontSize="8" fill="#d97706" fontWeight="bold">SUPPORT</text>

              {/* Growth chart */}
              <polyline points="370,160 380,155 390,150 400,145 410,140 420,135"
                       stroke="#f59e0b" strokeWidth="2" fill="none"/>
              <text x="395" y="165" textAnchor="middle" fontSize="6" fill="#d97706">+24% Growth</text>

              {/* 24/7 support */}
              <circle cx="415" cy="150" r="8" fill="#10b981"/>
              <text x="415" y="153" textAnchor="middle" fontSize="5" fill="white">24/7</text>

              <text x="400" y="170" textAnchor="middle" fontSize="7" fill="#d97706">ROI: +156%</text>

              {/* Connection lines */}
              <line x1="112" y1="100" x2="188" y2="100" stroke="#3b82f6" strokeWidth="2" opacity="0.6"/>
              <line x1="212" y1="100" x2="288" y2="100" stroke="#8b5cf6" strokeWidth="2" opacity="0.6"/>
              <line x1="312" y1="100" x2="388" y2="100" stroke="#10b981" strokeWidth="2" opacity="0.6"/>

              {/* Overall progress bar */}
              <rect x="50" y="220" width="400" height="20" rx="10" fill="#f1f5f9" stroke="#e5e7eb"/>
              <rect x="50" y="220" width="280" height="20" rx="10" fill="url(#progressGradient)"/>
              <text x="250" y="233" textAnchor="middle" fontSize="10" fill="#1f2937" fontWeight="bold">Overall Progress: 70%</text>

              {/* Status indicators */}
              <rect x="50" y="260" width="400" height="80" rx="8" fill="#f8fafc"/>
              <text x="250" y="275" textAnchor="middle" fontSize="12" fill="#1f2937" fontWeight="bold">System Status</text>

              {/* Key metrics */}
              <rect x="70" y="285" width="80" height="40" rx="6" fill="#dbeafe"/>
              <text x="110" y="300" textAnchor="middle" fontSize="8" fill="#1d4ed8" fontWeight="bold">Leads</text>
              <text x="110" y="315" textAnchor="middle" fontSize="12" fill="#1d4ed8" fontWeight="bold">+47%</text>

              <rect x="170" y="285" width="80" height="40" rx="6" fill="#f3e8ff"/>
              <text x="210" y="300" textAnchor="middle" fontSize="8" fill="#7c3aed" fontWeight="bold">Conversion</text>
              <text x="210" y="315" textAnchor="middle" fontSize="12" fill="#7c3aed" fontWeight="bold">+32%</text>

              <rect x="270" y="285" width="80" height="40" rx="6" fill="#f0fdf4"/>
              <text x="310" y="300" textAnchor="middle" fontSize="8" fill="#059669" fontWeight="bold">Efficiency</text>
              <text x="310" y="315" textAnchor="middle" fontSize="12" fill="#059669" fontWeight="bold">+68%</text>

              <rect x="370" y="285" width="80" height="40" rx="6" fill="#fff7ed"/>
              <text x="410" y="300" textAnchor="middle" fontSize="8" fill="#d97706" fontWeight="bold">Revenue</text>
              <text x="410" y="315" textAnchor="middle" fontSize="12" fill="#d97706" fontWeight="bold">+156%</text>

              {/* Animated elements */}
              <circle cx="470" cy="30" r="3" fill="#10b981">
                <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
              </circle>
              <text x="460" y="33" fontSize="6" fill="#10b981">ACTIVE</text>

              {/* Gradient definition */}
              <defs>
                <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#3b82f6"/>
                  <stop offset="33%" stopColor="#8b5cf6"/>
                  <stop offset="66%" stopColor="#10b981"/>
                  <stop offset="100%" stopColor="#f59e0b"/>
                </linearGradient>
              </defs>
            </svg>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default ImplementationProcessSection;
