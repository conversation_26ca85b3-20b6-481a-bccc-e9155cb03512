import React from 'react';
import Image from 'next/image';
import { Section, SectionHeader, Card } from '@/components/ui';
import { services } from '@/data/services';

const OurServicesSection: React.FC = () => {
  return (
    <Section
      id="our-services"
      padding="xl"
      background="gray"
    >
      <SectionHeader
        subtitle="Our services"
        title="We are here to help"
        align="center"
      />

      <div className="grid md:grid-cols-3 gap-8">
        {services.map((service) => (
          <Card
            key={service.id}
            variant="elevated"
            padding="lg"
            hover
            className="text-center"
          >
            <div className="space-y-6">
              {/* Icon */}
              <div className="flex justify-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
                  <Image
                    src={service.icon}
                    alt={`${service.title} icon`}
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                </div>
              </div>

              {/* Content */}
              <div className="space-y-3">
                <h3 className="text-xl font-bold text-gray-900">
                  {service.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {service.description}
                </p>
              </div>
            </div>
          </Card>
        ))}
      </div>


    </Section>
  );
};

export default OurServicesSection;
