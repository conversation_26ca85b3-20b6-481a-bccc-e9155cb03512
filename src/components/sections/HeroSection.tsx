import React from 'react';
import Image from 'next/image';
import { Button, Section, Container } from '@/components/ui';
import { heroContent } from '@/data/content';

const HeroSection: React.FC = () => {
  return (
    <Section
      id="hero"
      padding="xl"
      background="transparent"
      className="relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-1/2 h-full opacity-10">
          <Image
            src="/images/setmee-bg-1.png"
            alt="Background pattern"
            fill
            sizes="(max-width: 1280px) 50vw, 640px"
            className="object-cover object-right"
            priority
          />
        </div>
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-white via-white/90 to-transparent"></div>
      </div>

      <Container className="relative z-10">
        <div>
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight whitespace-pre-line">
                {heroContent.title}
              </h1>
              <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-2xl">
                {heroContent.description}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                href={heroContent.ctaLink}
                variant="primary"
                size="lg"
                className="text-lg px-8 py-4"
              >
                {heroContent.ctaText}
              </Button>
            </div>

            {/* Trust indicators */}
            <div className="pt-8 border-t border-gray-200">
              <div className="flex items-center space-x-4 mb-4">
                <Image
                  src="/images/Dark transparent SVG.svg"
                  alt="Certified Kommo Partner"
                  width={160}
                  height={48}
                  className="object-contain"
                />
                <p className="text-base text-gray-500 font-medium">
                  Certified Kommo Partner
                </p>
              </div>
              <div className="flex items-center space-x-8 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>10+ Years Experience</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>320+ Projects</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>4000+ Managers Trained</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </Section>
  );
};

export default HeroSection;
