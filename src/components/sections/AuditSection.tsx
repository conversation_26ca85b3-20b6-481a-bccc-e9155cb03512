import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@/components/ui';

const AuditSection: React.FC = () => {
  return (
    <Section
      id="audit"
      padding="xl"
      background="gray"
    >
      <div className="grid lg:grid-cols-2 gap-12 items-center">
        {/* Visual */}
        <div className="relative order-2 lg:order-1">
          <div className="relative">
            {/* Main Dashboard Mockup */}
            <div className="w-full h-96 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 rounded-2xl shadow-lg p-6 border border-gray-200">
              <svg viewBox="0 0 400 300" className="w-full h-full">
                {/* Dashboard background */}
                <rect x="0" y="0" width="400" height="300" rx="12" fill="white" stroke="#e5e7eb" strokeWidth="2"/>

                {/* Header */}
                <rect x="0" y="0" width="400" height="50" rx="12" fill="#3b82f6"/>
                <circle cx="20" cy="25" r="6" fill="white" opacity="0.9"/>
                <circle cx="35" cy="25" r="6" fill="white" opacity="0.7"/>
                <circle cx="50" cy="25" r="6" fill="white" opacity="0.5"/>
                <text x="80" y="30" fontSize="14" fill="white" fontWeight="bold">Kommo CRM Dashboard</text>

                {/* Navigation */}
                <rect x="20" y="70" width="60" height="25" rx="4" fill="#f3f4f6"/>
                <text x="50" y="85" textAnchor="middle" fontSize="10" fill="#374151">Leads</text>
                <rect x="90" y="70" width="60" height="25" rx="4" fill="#dbeafe"/>
                <text x="120" y="85" textAnchor="middle" fontSize="10" fill="#1d4ed8">Pipeline</text>
                <rect x="160" y="70" width="60" height="25" rx="4" fill="#f3f4f6"/>
                <text x="190" y="85" textAnchor="middle" fontSize="10" fill="#374151">Reports</text>

                {/* Main content area - Pipeline */}
                <rect x="20" y="110" width="240" height="170" rx="8" fill="#f8fafc" stroke="#e2e8f0"/>

                {/* Pipeline stages */}
                <rect x="30" y="125" width="50" height="140" rx="6" fill="#ecfdf5" stroke="#10b981"/>
                <text x="55" y="140" textAnchor="middle" fontSize="8" fill="#059669" fontWeight="bold">New Leads</text>
                <rect x="35" y="150" width="40" height="15" rx="3" fill="#10b981" opacity="0.8"/>
                <rect x="35" y="170" width="40" height="15" rx="3" fill="#10b981" opacity="0.6"/>
                <rect x="35" y="190" width="40" height="15" rx="3" fill="#10b981" opacity="0.4"/>
                <text x="55" y="220" textAnchor="middle" fontSize="10" fill="#059669" fontWeight="bold">24</text>

                <rect x="90" y="125" width="50" height="140" rx="6" fill="#fef3c7" stroke="#f59e0b"/>
                <text x="115" y="140" textAnchor="middle" fontSize="8" fill="#d97706" fontWeight="bold">Qualified</text>
                <rect x="95" y="150" width="40" height="15" rx="3" fill="#f59e0b" opacity="0.8"/>
                <rect x="95" y="170" width="40" height="15" rx="3" fill="#f59e0b" opacity="0.6"/>
                <text x="115" y="200" textAnchor="middle" fontSize="10" fill="#d97706" fontWeight="bold">18</text>

                <rect x="150" y="125" width="50" height="140" rx="6" fill="#dbeafe" stroke="#3b82f6"/>
                <text x="175" y="140" textAnchor="middle" fontSize="8" fill="#1d4ed8" fontWeight="bold">Proposal</text>
                <rect x="155" y="150" width="40" height="15" rx="3" fill="#3b82f6" opacity="0.8"/>
                <rect x="155" y="170" width="40" height="15" rx="3" fill="#3b82f6" opacity="0.6"/>
                <text x="175" y="200" textAnchor="middle" fontSize="10" fill="#1d4ed8" fontWeight="bold">12</text>

                <rect x="210" y="125" width="40" height="140" rx="6" fill="#f3e8ff" stroke="#8b5cf6"/>
                <text x="230" y="140" textAnchor="middle" fontSize="8" fill="#7c3aed" fontWeight="bold">Closed</text>
                <rect x="215" y="150" width="30" height="15" rx="3" fill="#8b5cf6" opacity="0.8"/>
                <text x="230" y="180" textAnchor="middle" fontSize="10" fill="#7c3aed" fontWeight="bold">8</text>

                {/* Sidebar - Analytics */}
                <rect x="280" y="110" width="100" height="170" rx="8" fill="#f9fafb" stroke="#e5e7eb"/>
                <text x="330" y="130" textAnchor="middle" fontSize="10" fill="#374151" fontWeight="bold">Analytics</text>

                {/* Mini charts */}
                <rect x="290" y="140" width="80" height="40" rx="4" fill="white" stroke="#e5e7eb"/>
                <polyline points="295,170 305,160 315,165 325,155 335,150 345,145 355,140 365,145"
                         stroke="#10b981" strokeWidth="2" fill="none"/>
                <text x="330" y="185" textAnchor="middle" fontSize="8" fill="#059669">Conversion Rate</text>

                <rect x="290" y="195" width="80" height="40" rx="4" fill="white" stroke="#e5e7eb"/>
                <rect x="295" y="225" width="8" height="8" fill="#3b82f6"/>
                <rect x="305" y="220" width="8" height="13" fill="#3b82f6"/>
                <rect x="315" y="215" width="8" height="18" fill="#3b82f6"/>
                <rect x="325" y="210" width="8" height="23" fill="#3b82f6"/>
                <rect x="335" y="205" width="8" height="28" fill="#3b82f6"/>
                <rect x="345" y="218" width="8" height="15" fill="#3b82f6"/>
                <rect x="355" y="212" width="8" height="21" fill="#3b82f6"/>
                <text x="330" y="250" textAnchor="middle" fontSize="8" fill="#1d4ed8">Revenue Trend</text>

                {/* Status indicators */}
                <circle cx="290" cy="265" r="4" fill="#10b981"/>
                <text x="300" y="268" fontSize="8" fill="#059669">System Optimized</text>

                <circle cx="290" cy="275" r="4" fill="#f59e0b"/>
                <text x="300" y="278" fontSize="8" fill="#d97706">3 Pending Tasks</text>

                {/* Animated elements */}
                <circle cx="370" cy="120" r="3" fill="#10b981">
                  <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
                </circle>
              </svg>
            </div>

            {/* Overlay card */}
            <div className="absolute -bottom-6 -right-6 bg-white rounded-xl shadow-xl p-6 max-w-xs">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-700">Audit Report Ready</span>
                </div>
                <div className="text-xs text-gray-500">
                  ✓ Funnel settings analyzed<br/>
                  ✓ Technical errors identified<br/>
                  ✓ Analytics optimized
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-8 order-1 lg:order-2">
          <SectionHeader
            title="You are a Kommo user already, but not sure if you get the most out of your system?"
            align="left"
            className="mb-8"
          />

          <div className="space-y-6">
            <Card variant="flat" padding="lg" className="border-l-4 border-l-primary-500">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  {/* Audit Dashboard SVG */}
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-2">
                    <svg viewBox="0 0 60 60" className="w-full h-full">
                      {/* Dashboard background */}
                      <rect x="4" y="8" width="52" height="44" rx="4" fill="#f8fafc" stroke="#e2e8f0" strokeWidth="1"/>

                      {/* Header bar */}
                      <rect x="4" y="8" width="52" height="8" rx="4" fill="#3b82f6"/>
                      <circle cx="12" cy="12" r="1.5" fill="white"/>
                      <circle cx="16" cy="12" r="1.5" fill="white"/>
                      <circle cx="20" cy="12" r="1.5" fill="white"/>

                      {/* Audit checklist items */}
                      <rect x="8" y="20" width="20" height="3" rx="1.5" fill="#10b981"/>
                      <rect x="8" y="26" width="16" height="3" rx="1.5" fill="#f59e0b"/>
                      <rect x="8" y="32" width="18" height="3" rx="1.5" fill="#ef4444"/>
                      <rect x="8" y="38" width="14" height="3" rx="1.5" fill="#6b7280"/>

                      {/* Checkmarks and crosses */}
                      <circle cx="32" cy="21.5" r="2" fill="#10b981"/>
                      <path d="M30.5 21.5l1 1 2-2" stroke="white" strokeWidth="0.8" fill="none"/>

                      <circle cx="32" cy="27.5" r="2" fill="#f59e0b"/>
                      <text x="32" y="29" textAnchor="middle" fontSize="3" fill="white">!</text>

                      <circle cx="32" cy="33.5" r="2" fill="#ef4444"/>
                      <path d="M30.5 32l3 3M33.5 32l-3 3" stroke="white" strokeWidth="0.8"/>

                      {/* Progress indicator */}
                      <rect x="40" y="20" width="12" height="20" rx="2" fill="#f1f5f9" stroke="#e2e8f0"/>
                      <rect x="42" y="22" width="8" height="4" rx="1" fill="#3b82f6"/>
                      <rect x="42" y="28" width="6" height="4" rx="1" fill="#10b981"/>
                      <rect x="42" y="34" width="4" height="4" rx="1" fill="#f59e0b"/>

                      {/* Scanning animation dots */}
                      <circle cx="46" cy="45" r="1" fill="#3b82f6" opacity="0.8">
                        <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite"/>
                      </circle>
                      <circle cx="50" cy="45" r="1" fill="#3b82f6" opacity="0.6">
                        <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" begin="0.3s" repeatCount="indefinite"/>
                      </circle>
                    </svg>
                  </div>
                </div>
                <div className="space-y-3 flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Audit your system
                  </h3>
                  <p className="text-gray-600">
                    During the audit, our experts will check your system against an impressive checklist of settings:
                  </p>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• funnel settings</li>
                    <li>• overdue transactions and tasks</li>
                    <li>• logical and technical errors</li>
                    <li>• analytics failures and so on</li>
                  </ul>
                </div>
              </div>
            </Card>

            <Card variant="flat" padding="lg" className="border-l-4 border-l-secondary-500">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  {/* Analytics Dashboard SVG */}
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-2">
                    <svg viewBox="0 0 60 60" className="w-full h-full">
                      {/* Dashboard background */}
                      <rect x="4" y="8" width="52" height="44" rx="4" fill="#f8fafc" stroke="#e2e8f0" strokeWidth="1"/>

                      {/* Header bar */}
                      <rect x="4" y="8" width="52" height="8" rx="4" fill="#8b5cf6"/>
                      <circle cx="12" cy="12" r="1.5" fill="white"/>
                      <circle cx="16" cy="12" r="1.5" fill="white"/>
                      <circle cx="20" cy="12" r="1.5" fill="white"/>

                      {/* Charts and graphs */}
                      <rect x="8" y="20" width="20" height="12" rx="2" fill="#f3f4f6" stroke="#e5e7eb"/>

                      {/* Bar chart */}
                      <rect x="10" y="28" width="2" height="3" fill="#8b5cf6"/>
                      <rect x="13" y="26" width="2" height="5" fill="#8b5cf6"/>
                      <rect x="16" y="24" width="2" height="7" fill="#8b5cf6"/>
                      <rect x="19" y="22" width="2" height="9" fill="#8b5cf6"/>
                      <rect x="22" y="25" width="2" height="6" fill="#8b5cf6"/>
                      <rect x="25" y="23" width="2" height="8" fill="#8b5cf6"/>

                      {/* Pie chart */}
                      <circle cx="42" cy="26" r="6" fill="#f3f4f6" stroke="#e5e7eb"/>
                      <path d="M42 20 A6 6 0 0 1 47 23 L42 26 Z" fill="#8b5cf6"/>
                      <path d="M47 23 A6 6 0 0 1 45 31 L42 26 Z" fill="#06b6d4"/>
                      <path d="M45 31 A6 6 0 0 1 37 29 L42 26 Z" fill="#10b981"/>
                      <path d="M37 29 A6 6 0 0 1 42 20 L42 26 Z" fill="#f59e0b"/>

                      {/* Metrics cards */}
                      <rect x="8" y="36" width="12" height="8" rx="2" fill="#ecfdf5" stroke="#10b981"/>
                      <text x="14" y="41" textAnchor="middle" fontSize="4" fill="#10b981" fontWeight="bold">85%</text>
                      <text x="14" y="44" textAnchor="middle" fontSize="2.5" fill="#059669">Efficiency</text>

                      <rect x="22" y="36" width="12" height="8" rx="2" fill="#fef3c7" stroke="#f59e0b"/>
                      <text x="28" y="41" textAnchor="middle" fontSize="4" fill="#f59e0b" fontWeight="bold">12</text>
                      <text x="28" y="44" textAnchor="middle" fontSize="2.5" fill="#d97706">Issues</text>

                      <rect x="36" y="36" width="16" height="8" rx="2" fill="#dbeafe" stroke="#3b82f6"/>
                      <text x="44" y="41" textAnchor="middle" fontSize="4" fill="#3b82f6" fontWeight="bold">+24%</text>
                      <text x="44" y="44" textAnchor="middle" fontSize="2.5" fill="#2563eb">Growth</text>

                      {/* Report ready indicator */}
                      <circle cx="50" cy="22" r="3" fill="#10b981"/>
                      <path d="M48.5 22l1 1 2-2" stroke="white" strokeWidth="0.8" fill="none"/>
                    </svg>
                  </div>
                </div>
                <div className="space-y-3 flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Report with audit results
                  </h3>
                  <p className="text-gray-600">
                    With a bit of help, your Insights section will give you all the data you need.
                  </p>
                </div>
              </div>
            </Card>

            <Card variant="flat" padding="lg" className="border-l-4 border-l-green-500">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  {/* Optimized System Dashboard SVG */}
                  <div className="w-16 h-16 bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-2">
                    <svg viewBox="0 0 60 60" className="w-full h-full">
                      {/* Dashboard background */}
                      <rect x="4" y="8" width="52" height="44" rx="4" fill="#f8fafc" stroke="#e2e8f0" strokeWidth="1"/>

                      {/* Header bar */}
                      <rect x="4" y="8" width="52" height="8" rx="4" fill="#10b981"/>
                      <circle cx="12" cy="12" r="1.5" fill="white"/>
                      <circle cx="16" cy="12" r="1.5" fill="white"/>
                      <circle cx="20" cy="12" r="1.5" fill="white"/>

                      {/* Optimized funnel */}
                      <rect x="8" y="20" width="20" height="24" rx="2" fill="#f0fdf4" stroke="#10b981"/>

                      {/* Funnel stages */}
                      <rect x="10" y="22" width="16" height="3" rx="1.5" fill="#10b981"/>
                      <rect x="11" y="27" width="14" height="3" rx="1.5" fill="#10b981"/>
                      <rect x="12" y="32" width="12" height="3" rx="1.5" fill="#10b981"/>
                      <rect x="13" y="37" width="10" height="3" rx="1.5" fill="#10b981"/>
                      <rect x="14" y="42" width="8" height="2" rx="1" fill="#10b981"/>

                      {/* Conversion rates */}
                      <text x="30" y="24" fontSize="2.5" fill="#10b981" fontWeight="bold">95%</text>
                      <text x="30" y="29" fontSize="2.5" fill="#10b981" fontWeight="bold">87%</text>
                      <text x="30" y="34" fontSize="2.5" fill="#10b981" fontWeight="bold">78%</text>
                      <text x="30" y="39" fontSize="2.5" fill="#10b981" fontWeight="bold">65%</text>
                      <text x="30" y="44" fontSize="2.5" fill="#10b981" fontWeight="bold">52%</text>

                      {/* Performance indicators */}
                      <rect x="32" y="20" width="20" height="10" rx="2" fill="#ecfdf5" stroke="#10b981"/>

                      {/* Speed indicator */}
                      <circle cx="38" cy="25" r="3" fill="#10b981"/>
                      <path d="M36 25 L38 23 L40 25" stroke="white" strokeWidth="0.8" fill="none"/>
                      <text x="42" y="26" fontSize="2.5" fill="#10b981">Fast</text>

                      {/* Automation indicators */}
                      <rect x="32" y="32" width="8" height="6" rx="1" fill="#dcfce7" stroke="#10b981"/>
                      <circle cx="36" cy="35" r="1.5" fill="#10b981"/>
                      <path d="M34.5 35l1 1 2-2" stroke="white" strokeWidth="0.6" fill="none"/>
                      <text x="42" y="36" fontSize="2.5" fill="#10b981">Auto</text>

                      <rect x="32" y="40" width="8" height="6" rx="1" fill="#dcfce7" stroke="#10b981"/>
                      <circle cx="36" cy="43" r="1.5" fill="#10b981"/>
                      <path d="M34.5 43l1 1 2-2" stroke="white" strokeWidth="0.6" fill="none"/>
                      <text x="42" y="44" fontSize="2.5" fill="#10b981">Smart</text>

                      {/* Training/user icon */}
                      <circle cx="48" cy="35" r="3" fill="#10b981"/>
                      <circle cx="48" cy="33" r="1.5" fill="white"/>
                      <path d="M45 38 Q48 36 51 38" stroke="white" strokeWidth="0.8" fill="none"/>

                      {/* Success pulse animation */}
                      <circle cx="48" cy="25" r="2" fill="#10b981" opacity="0.6">
                        <animate attributeName="r" values="2;4;2" dur="2s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite"/>
                      </circle>
                    </svg>
                  </div>
                </div>
                <div className="space-y-3 flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Realization
                  </h3>
                  <p className="text-gray-600">
                    Our experts will set up the system so that it works with maximum efficiency and will conduct training sessions on working in the system.
                  </p>
                </div>
              </div>
            </Card>
          </div>

          <div className="pt-6">
            <Button
              href="/contact"
              variant="primary"
              size="lg"
            >
              Contact a specialist
            </Button>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default AuditSection;
