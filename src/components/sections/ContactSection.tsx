import React from 'react';
import { Section, Button } from '@/components/ui';

const ContactSection: React.FC = () => {
  return (
    <Section
      id="contact"
      padding="xl"
      background="primary"
      className="text-white"
    >
      <div className="text-center space-y-8">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-24 -left-24 w-48 h-48 bg-secondary-500 rounded-full opacity-10"></div>
          <div className="absolute -bottom-24 -right-24 w-48 h-48 bg-primary-300 rounded-full opacity-10"></div>
          <div className="absolute top-1/2 left-1/4 w-32 h-32 bg-secondary-400 rounded-full opacity-5"></div>
          <div className="absolute top-1/4 right-1/3 w-24 h-24 bg-primary-400 rounded-full opacity-5"></div>
        </div>

        <div className="relative z-10 max-w-4xl mx-auto space-y-8">
          {/* Main content */}
          <div className="space-y-6">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight">
              Contact us if you have any questions
            </h2>
            <p className="text-lg md:text-xl text-primary-100 leading-relaxed max-w-2xl mx-auto">
              Ready to transform your business with Kommo CRM? Our certified experts are here to help you every step of the way.
            </p>
          </div>

          {/* CTA Button */}
          <div className="pt-4">
            <Button
              href="/contact"
              variant="secondary"
              size="lg"
              className="text-xl px-12 py-5"
            >
              Talk to an expert
            </Button>
          </div>

          {/* Contact info */}
          <div className="pt-8 border-t border-primary-700">
            <div className="grid md:grid-cols-3 gap-8 text-center">
              <div className="space-y-3">
                <div className="w-12 h-12 bg-primary-700 rounded-lg flex items-center justify-center mx-auto">
                  <svg className="w-6 h-6 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-lg">Email Us</h3>
                  <p className="text-primary-200"><EMAIL></p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="w-12 h-12 bg-primary-700 rounded-lg flex items-center justify-center mx-auto">
                  <svg className="w-6 h-6 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-lg">Response Time</h3>
                  <p className="text-primary-200">Within 24 hours</p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="w-12 h-12 bg-primary-700 rounded-lg flex items-center justify-center mx-auto">
                  <svg className="w-6 h-6 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-lg">Certified Partner</h3>
                  <p className="text-primary-200">Official Kommo Partner</p>
                </div>
              </div>
            </div>
          </div>

          {/* Trust indicators */}
          <div className="pt-8">
            <div className="bg-primary-700 rounded-xl p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-secondary-400">10+</div>
                  <div className="text-sm text-primary-200">Years Experience</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-secondary-400">320+</div>
                  <div className="text-sm text-primary-200">Projects Completed</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-secondary-400">4000+</div>
                  <div className="text-sm text-primary-200">Managers Trained</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-secondary-400">30+</div>
                  <div className="text-sm text-primary-200">Custom Integrations</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default ContactSection;
