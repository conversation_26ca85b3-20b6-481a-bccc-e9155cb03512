import React from 'react';
import Image from 'next/image';
import { Section } from '@/components/ui';
import { companyStats } from '@/data/content';

const AboutSection: React.FC = () => {
  return (
    <Section
      id="who-are-we"
      padding="xl"
      background="primary"
      className="text-white"
    >
      <div className="grid lg:grid-cols-2 gap-12 items-center">
        {/* Certificate */}
        <div className="relative">
          <div className="bg-white rounded-2xl p-6 shadow-2xl">
            <div className="text-center space-y-4">
              <p className="text-sm font-semibold text-primary-600 uppercase tracking-wide">
                certificate of partnership
              </p>
              <h3 className="text-xl font-bold text-gray-900">
                Who we are
              </h3>
              
              <div className="relative">
                <Image
                  src="/images/certificate.jpeg"
                  alt="Kommo Partnership Certificate"
                  width={400}
                  height={300}
                  className="w-full h-auto rounded-lg shadow-md"
                />
              </div>
            </div>
          </div>

          {/* Floating badge */}
          <div className="absolute -top-4 -right-4 bg-secondary-500 text-white rounded-full p-4 shadow-lg">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-8">
          <div className="space-y-6">
            <h2 className="text-3xl md:text-4xl font-bold">
              Setmee is a certified partner of Kommo
            </h2>
            <p className="text-lg text-primary-100 leading-relaxed">
              We are a team of specialists specialising in workflow automation and Kommo-based sales. 
              We are committed to solving complex problems and interesting cases.
            </p>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-2 gap-6">
            {companyStats.map((stat) => (
              <div key={stat.id} className="text-center space-y-2">
                <div className="text-3xl md:text-4xl font-bold text-secondary-400">
                  {stat.value}
                </div>
                <div className="text-sm text-primary-200 uppercase tracking-wide">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>

          {/* Additional info */}
          <div className="bg-primary-700 rounded-xl p-6 space-y-4">
            <h4 className="text-lg font-semibold">Why choose Setmee?</h4>
            <ul className="space-y-2 text-primary-100">
              <li className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-secondary-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>Certified Kommo partnership</span>
              </li>
              <li className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-secondary-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>10 years of proven experience</span>
              </li>
              <li className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-secondary-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>Complex problem-solving expertise</span>
              </li>
              <li className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-secondary-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>Custom integration development</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default AboutSection;
