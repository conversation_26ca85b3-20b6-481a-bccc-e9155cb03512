// Базовые типы для проекта Setmee Landing

export interface Service {
  id: string;
  title: string;
  description: string;
  icon: string;
}

export interface ProcessStep {
  id: string;
  title: string;
  description: string;
  order: number;
}

export interface Statistic {
  id: string;
  value: string;
  label: string;
}

export interface Feature {
  id: string;
  title: string;
  description: string;
  icon: string;
}

export interface BusinessVertical {
  id: string;
  title: string;
  image: string;
}

export interface Integration {
  id: string;
  name: string;
  logo: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  message: string;
}

export interface NavigationItem {
  label: string;
  href: string;
}
