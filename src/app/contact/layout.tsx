import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Contact Our Kommo Experts | Free CRM Consultation | Setmee',
  description: 'Get in touch with our certified Kommo specialists for a free consultation. Expert CRM implementation, optimization, and custom integrations. Response within 24 hours.',
  openGraph: {
    title: 'Contact Our Kommo Experts | Free CRM Consultation | Setmee',
    description: 'Get in touch with our certified Kommo specialists for a free consultation. Expert CRM implementation, optimization, and custom integrations.',
    url: 'https://setmee.com/contact',
  },
  alternates: {
    canonical: '/contact',
  },
};

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
