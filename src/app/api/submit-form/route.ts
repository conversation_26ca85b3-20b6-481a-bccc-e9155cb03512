import { NextRequest, NextResponse } from 'next/server';
import { getFormConfig, isWebhookConfigured } from '@/config/forms';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { formType, formData } = body;

    if (!formType || !formData) {
      return NextResponse.json(
        { success: false, message: 'Missing formType or formData' },
        { status: 400 }
      );
    }

    const config = getFormConfig(formType);

    // Проверяем, настроен ли webhook
    if (!isWebhookConfigured(formType)) {
      console.warn(`Webhook для формы ${formType} не настроен. Данные логируются в консоль.`);
      console.log(`${formType} form data:`, formData);
      
      return NextResponse.json({
        success: true,
        message: config.successMessage,
      });
    }

    // Подготавливаем данные для отправки
    const payload = {
      formType,
      timestamp: new Date().toISOString(),
      source: 'setmee-website',
      ...formData
    };

    console.log(`Отправка ${formType} формы на webhook:`, config.webhookUrl);
    console.log(`${formType} payload:`, payload);

    // Отправляем данные на webhook
    const response = await fetch(config.webhookUrl!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`HTTP error! status: ${response.status}, response:`, errorText);
      
      return NextResponse.json({
        success: false,
        message: config.errorMessage,
      }, { status: 500 });
    }

    console.log(`${formType} form submitted successfully to Make.com`);
    
    return NextResponse.json({
      success: true,
      message: config.successMessage,
    });

  } catch (error) {
    console.error('Form submission error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'An unexpected error occurred. Please try again.',
    }, { status: 500 });
  }
}
