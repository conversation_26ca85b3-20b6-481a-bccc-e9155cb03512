# Примеры использования форм

## 🔧 Базовое использование

### Контактная форма
```typescript
import { submitContactForm } from '@/lib/form-service';
import { ContactFormData } from '@/lib/validations';

const handleSubmit = async (data: ContactFormData) => {
  const result = await submitContactForm(data);
  
  if (result.success) {
    console.log('Form submitted successfully!');
    // Показать сообщение об успехе
  } else {
    console.error('Sending error:', result.message);
    // Показать сообщение об ошибке
  }
};
```

### Форма подписки
```typescript
import { submitEmailSubscription } from '@/lib/form-service';
import { EmailSubscriptionData } from '@/lib/validations';

const handleSubscribe = async (data: EmailSubscriptionData) => {
  const result = await submitEmailSubscription(data);
  
  if (result.success) {
    console.log('Subscription completed!');
  } else {
    console.error('Subscription error:', result.message);
  }
};
```

## 🎯 Универсальная функция

```typescript
import { submitForm } from '@/lib/form-service';

// Отправка контактной формы
const contactResult = await submitForm('contact', {
  name: 'Иван Петров',
  email: '<EMAIL>',
  phone: '****** 123-45-67',
  company: 'ООО Компания',
  message: 'Интересует внедрение Kommo'
});

// Отправка формы подписки
const newsletterResult = await submitForm('newsletter', {
  email: '<EMAIL>'
});
```

## 🧪 Проверка конфигурации

```typescript
import { isWebhookConfigured, getFormConfig } from '@/config/forms';

// Проверить, настроен ли webhook
if (isWebhookConfigured('contact')) {
  console.log('Contact webhook настроен');
} else {
  console.log('Contact webhook НЕ настроен - данные будут логироваться');
}

// Получить конфигурацию формы
const config = getFormConfig('contact');
console.log('Сообщение об успехе:', config.successMessage);
console.log('Сообщение об ошибке:', config.errorMessage);
```

## 📝 Полный пример компонента

```typescript
'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { contactFormSchema, ContactFormData } from '@/lib/validations';
import { submitContactForm } from '@/lib/form-service';

const ContactForm: React.FC = () => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [message, setMessage] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
  });

  const onSubmit = async (data: ContactFormData) => {
    try {
      const result = await submitContactForm(data);

      if (result.success) {
        setIsSubmitted(true);
        setMessage(result.message);
        reset();
      } else {
        setMessage(result.message);
      }
    } catch {
      setMessage('Произошла неожиданная ошибка. Пожалуйста, попробуйте еще раз.');
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {isSubmitted && (
        <div className="success-message">
          {message}
        </div>
      )}

      <input
        {...register('name')}
        placeholder="Ваше имя"
      />
      {errors.name && <span>{errors.name.message}</span>}

      <input
        {...register('email')}
        type="email"
        placeholder="Email"
      />
      {errors.email && <span>{errors.email.message}</span>}

      <textarea
        {...register('message')}
        placeholder="Сообщение"
      />
      {errors.message && <span>{errors.message.message}</span>}

      <button type="submit" disabled={isSubmitting}>
        {isSubmitting ? 'Отправка...' : 'Отправить'}
      </button>
    </form>
  );
};

export default ContactForm;
```

## 🔄 Обработка ошибок

```typescript
import { submitForm } from '@/lib/form-service';

const handleFormSubmit = async (formData: any) => {
  try {
    const result = await submitForm('contact', formData);
    
    if (result.success) {
      // Успешная отправка
      showSuccessNotification(result.message);
      resetForm();
    } else {
      // Ошибка от сервера
      showErrorNotification(result.message);
    }
  } catch (error) {
    // Неожиданная ошибка (сеть, парсинг и т.д.)
    console.error('Unexpected error:', error);
    showErrorNotification('Произошла неожиданная ошибка. Попробуйте позже.');
  }
};
```

## 🎨 Кастомизация сообщений

Сообщения настраиваются в `src/config/forms.ts`:

```typescript
export const formConfigs = {
  contact: {
    successMessage: 'Ваше сообщение отправлено! Мы ответим в течение 24 часов.',
    errorMessage: 'Не удалось отправить сообщение. Попробуйте еще раз.',
    // ...
  },
  newsletter: {
    successMessage: 'Спасибо за подписку! Презентация будет отправлена на email.',
    errorMessage: 'Ошибка подписки. Проверьте email и попробуйте снова.',
    // ...
  }
};
```
