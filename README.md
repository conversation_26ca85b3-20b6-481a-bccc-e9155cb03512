# Setmee Landing Page

A modern, responsive landing page for Setmee - a certified Kommo CRM partner specializing in implementation, optimization, training, and custom integrations.

## 🚀 Features

- **Modern Design**: Clean, professional design matching the original Setmee website
- **Fully Responsive**: Optimized for all devices (mobile, tablet, desktop)
- **SEO Optimized**: Complete meta tags, Open Graph, Twitter Cards, and structured data
- **Performance Optimized**: Built with Next.js 15 for optimal performance
- **Type Safe**: Full TypeScript implementation
- **Form Validation**: Client-side validation with <PERSON>od and React Hook Form
- **Accessibility**: WCAG compliant with proper semantic HTML

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Forms**: React Hook Form + Zod validation
- **Icons**: Heroicons (SVG)
- **Image Optimization**: Next.js Image component
- **Linting**: ESLint + Prettier

## 📁 Project Structure

```
setmee-landing/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── contact/           # Contact page
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   ├── page.tsx          # Home page
│   │   └── sitemap.ts        # Dynamic sitemap
│   ├── components/
│   │   ├── layout/           # Header, Footer
│   │   ├── sections/         # Page sections
│   │   └── ui/              # Reusable UI components
│   ├── data/                # Static data and content
│   ├── lib/                 # Utilities and validations
│   └── types/               # TypeScript type definitions
├── public/                  # Static assets
│   ├── images/             # Images and graphics
│   ├── icons/              # SVG icons
│   └── robots.txt          # SEO robots file
└── package.json
```

## 🚦 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd setmee-landing
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Start the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📝 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting

## 🎨 Customization

### Colors

The color scheme is defined in `src/app/globals.css` using CSS custom properties:

- **Primary**: Blue tones for main branding
- **Secondary**: Orange for CTAs and accents
- **Gray**: Various shades for text and backgrounds

### Content

All content is centralized in the `src/data/` directory:

- `content.ts` - Main page content
- `navigation.ts` - Navigation items
- `services.ts` - Service offerings
- `business-verticals.ts` - Business verticals

### Forms

Forms use React Hook Form with Zod validation and Make.com webhook integration:

- Validation schemas in `src/lib/validations.ts`
- Form service in `src/lib/form-service.ts`
- Form configuration in `src/config/forms.ts`
- **Make.com integration**: Forms automatically send data to configured webhooks
- Test page available at `/test-forms`

## 🔧 Configuration

### Make.com Integration (Recommended)

The project includes built-in Make.com webhook integration:

1. **Setup webhooks in Make.com**:
   - Create scenarios with "Custom webhook" triggers
   - Copy webhook URLs

2. **Configure environment variables**:
   ```bash
   # Copy .env.example to .env.local and add your webhook URLs
   NEXT_PUBLIC_MAKE_CONTACT_WEBHOOK=https://hook.eu1.make.com/your-contact-webhook-id
   NEXT_PUBLIC_MAKE_NEWSLETTER_WEBHOOK=https://hook.eu1.make.com/your-newsletter-webhook-id
   ```

3. **Usage**:
   ```typescript
   import { submitForm, submitContactForm } from '@/lib/form-service';

   // Universal function
   const result = await submitForm('contact', formData);

   // Specialized functions
   const contactResult = await submitContactForm(contactData);
   const newsletterResult = await submitEmailSubscription(emailData);
   ```

### Legacy Email Integration

Alternative email services (if not using Make.com):

1. **Formspree**: Uncomment and configure in `src/lib/form-service.ts`
2. **EmailJS**: Install EmailJS and configure
3. **Custom API**: Replace the webhook calls with your API endpoint

### SEO

Update SEO settings in:
- `src/app/layout.tsx` - Main metadata
- `src/app/contact/page.tsx` - Contact page metadata
- `src/components/StructuredData.tsx` - JSON-LD structured data

### Analytics

Add analytics by including scripts in `src/app/layout.tsx`:

```tsx
// Google Analytics example
<Script src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID" />
<Script id="google-analytics">
  {`
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'GA_MEASUREMENT_ID');
  `}
</Script>
```

## 🚀 Deployment

### Vercel (Recommended)

1. Push to GitHub
2. Connect repository to Vercel
3. Deploy automatically

### Other Platforms

1. Build the project:
```bash
npm run build
```

2. Deploy the `out` directory to your hosting platform

## 📊 Performance

The site is optimized for performance with:

- Next.js Image optimization
- Automatic code splitting
- CSS optimization
- Minimal JavaScript bundle
- Lazy loading of images

## 🔒 Security

- Form validation on both client and server side
- XSS protection through React
- CSRF protection for forms
- Secure headers configuration

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is proprietary and confidential.

## 📞 Support

For support or questions, contact the development team or visit [setmee.com](https://setmee.com).

---

Built with ❤️ by the Setmee team

<!-- Trigger Netlify rebuild -->
