#!/usr/bin/env node

/**
 * Performance Check Script
 * Runs basic performance checks on the built application
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Running Performance Checks...\n');

// Check if build exists
const buildPath = path.join(__dirname, '../.next');
if (!fs.existsSync(buildPath)) {
  console.error('❌ Build not found. Run `npm run build` first.');
  process.exit(1);
}

// Check bundle sizes
const staticPath = path.join(buildPath, 'static');
if (fs.existsSync(staticPath)) {
  console.log('📦 Bundle Analysis:');
  
  // Check JS bundles
  const jsPath = path.join(staticPath, 'chunks');
  if (fs.existsSync(jsPath)) {
    const jsFiles = fs.readdirSync(jsPath).filter(file => file.endsWith('.js'));
    const totalJSSize = jsFiles.reduce((total, file) => {
      const filePath = path.join(jsPath, file);
      const stats = fs.statSync(filePath);
      return total + stats.size;
    }, 0);
    
    console.log(`   JavaScript: ${(totalJSSize / 1024 / 1024).toFixed(2)} MB`);
    
    if (totalJSSize > 1024 * 1024) { // 1MB
      console.log('   ⚠️  Large JavaScript bundle detected');
    } else {
      console.log('   ✅ JavaScript bundle size is optimal');
    }
  }
  
  // Check CSS bundles
  const cssPath = path.join(staticPath, 'css');
  if (fs.existsSync(cssPath)) {
    const cssFiles = fs.readdirSync(cssPath).filter(file => file.endsWith('.css'));
    const totalCSSSize = cssFiles.reduce((total, file) => {
      const filePath = path.join(cssPath, file);
      const stats = fs.statSync(filePath);
      return total + stats.size;
    }, 0);
    
    console.log(`   CSS: ${(totalCSSSize / 1024).toFixed(2)} KB`);
    
    if (totalCSSSize > 100 * 1024) { // 100KB
      console.log('   ⚠️  Large CSS bundle detected');
    } else {
      console.log('   ✅ CSS bundle size is optimal');
    }
  }
}

// Check image optimization
const publicPath = path.join(__dirname, '../public');
console.log('\n🖼️  Image Analysis:');

function checkImages(dir, prefix = '') {
  const items = fs.readdirSync(dir);
  let totalSize = 0;
  let imageCount = 0;
  
  items.forEach(item => {
    const itemPath = path.join(dir, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      const result = checkImages(itemPath, `${prefix}${item}/`);
      totalSize += result.size;
      imageCount += result.count;
    } else if (/\.(jpg|jpeg|png|gif|webp|avif|svg)$/i.test(item)) {
      totalSize += stats.size;
      imageCount++;
      
      if (stats.size > 500 * 1024) { // 500KB
        console.log(`   ⚠️  Large image: ${prefix}${item} (${(stats.size / 1024).toFixed(2)} KB)`);
      }
    }
  });
  
  return { size: totalSize, count: imageCount };
}

const imageStats = checkImages(publicPath);
console.log(`   Total images: ${imageStats.count}`);
console.log(`   Total size: ${(imageStats.size / 1024 / 1024).toFixed(2)} MB`);

if (imageStats.size < 5 * 1024 * 1024) { // 5MB
  console.log('   ✅ Image sizes are optimal');
} else {
  console.log('   ⚠️  Consider optimizing images');
}

// Performance recommendations
console.log('\n💡 Performance Recommendations:');
console.log('   • Use Next.js Image component for all images');
console.log('   • Enable compression in production');
console.log('   • Use CDN for static assets');
console.log('   • Monitor Core Web Vitals');
console.log('   • Consider lazy loading for below-the-fold content');

console.log('\n✅ Performance check completed!');
console.log('\n📊 For detailed analysis, run:');
console.log('   npx @next/bundle-analyzer');
console.log('   npm run build -- --analyze');

console.log('\n🔍 Test your site with:');
console.log('   • Google PageSpeed Insights');
console.log('   • GTmetrix');
console.log('   • WebPageTest');
console.log('   • Lighthouse CI');
