# Техническое задание на копирование лендинга Setmee.com с использованием Next.js и TypeScript

## 1. Общие положения

Создать точную копию лендинга https://www.setmee.com/ на фреймворке Next.js с использованием TypeScript. Требуется обеспечить полное соответствие структуры, дизайна и функциональности оригинала, а также предоставить чистый, масштабируемый и легко поддерживаемый код.

## 2. Технологический стек

- **Фреймворк**: Next.js (актуальная LTS-версия)
- **Язык**: TypeScript (требование — весь проект только на TypeScript)
- **Стилизация**: Tailwind CSS (использовать utility-first подход, без сторонних UI-библиотек)
- **Контент**: статические данные размещать в отдельных JSON или TypeScript-файлах для удобства редактирования
- **Изображения и иконки**: хранить в папке `/public`, использовать Next.js Image для оптимизации
- **Управление состоянием**: не требуется, все компоненты должны быть максимально статичными

## 3. Структура проекта

```
/pages
  index.tsx — главная страница
  contact.tsx — страница контактов
  _app.tsx, _document.tsx — базовые файлы Next.js

/components
  Header.tsx — верхнее меню и навигация
  Footer.tsx — футер
  Секции главной страницы (см. ниже)
  Переиспользуемые элементы: Button.tsx, Card.tsx, ContactForm.tsx, и др.

/public
  Все изображения, иконки, favicon

/styles
  globals.css — Tailwind base + кастомные стили при необходимости

/data
  Статические данные для секций (например, списки преимуществ, услуг, вертикалей)
```

## 4. Страницы

### 4.1 Главная страница (`index.tsx`)

Содержит следующие секции, каждая реализуется отдельным компонентом:

1. **HeroSection** — главный баннер, призыв к действию, кнопка "Talk to an expert"
2. **OurServicesSection** — описание услуг (Kommo implementation, Optimisation of use, Customised integrations)
3. **ImplementationProcessSection** — этапы внедрения (Audit, System Setup, Training, Support)
4. **AuditSection** — аудит Kommo, чек-лист, отчет, реализация
5. **CustomIntegrationsSection** — кастомные интеграции, готовые решения, low-code
6. **AboutSection** — информация о компании, опыт, достижения (8 years, 320+ projects, 4000+ managers, 30+ integrations)
7. **WhyKommoSection** — преимущества Kommo (6 пунктов)
8. **FeaturesSection** — ключевые возможности Kommo (Simple interface, Digital pipeline, Plug in chat apps, Get context fast, Easy automation, Integrations, Analytics, Tasks/team chat/web forms)
9. **BusinessVerticalsSection** — сферы применения (Advertising, Automotive sales, B2B, B2C, Event management, Hospitality, Insurance, Logistics, Manufacturing, Marketing, SaaS and software, Real estate, Recruitment, Travel, Your business)
10. **CallToActionSection** — CTA-блок для презентации Kommo
11. **IntegrationsSection** — информация о 100+ интеграциях
12. **ContactSection** — финальный призыв связаться, кнопка "Talk to an expert"
13. **Footer** — копирайт, контакты

### 4.2 Страница контактов (`contact.tsx`)

- **ContactForm** — форма обратной связи (валидация на клиенте, отправка на email через интеграцию с Formspree или аналогом)
- **ContactInfo** — контактные данные компании

## 5. Функциональные требования

- **Навигация**: SPA-переходы между страницами через Header и Footer
- **Адаптивность**: корректное отображение на всех устройствах (desktop, tablet, mobile)
- **SEO**: для каждой страницы прописать уникальные title, description, Open Graph-теги через Next.js Head
- **Форма обратной связи**: обязательная валидация полей (имя, email, сообщение), отправка данных на указанный email, отображение статуса отправки (успех/ошибка)
- **Оптимизация изображений**: использовать компонент Next.js Image
- **Ленивая загрузка** изображений
- **Быстрая загрузка сайта**: использовать SSR/SSG (getStaticProps/getServerSideProps) по необходимости
- **Кроссбраузерность**: поддержка последних версий Chrome, Firefox, Safari, Edge

## 6. Дизайн и контент

- **Дизайн**: полностью соответствовать оригиналу по цветам, шрифтам, размерам, отступам, изображениям
- **Изображения**: использовать оригинальные или максимально похожие, оптимизированные по размеру
- **Тексты**: использовать оригинальные тексты лендинга на английском языке
- **Шрифты**: подключить те же, что на оригинале (Google Fonts либо локально)

## 7. Критерии приемки

- Полное визуальное и функциональное соответствие оригинальному лендингу
- Корректная работа всех форм и навигации
- Правильное отображение на всех актуальных устройствах и браузерах
- Структурированный и чистый код на TypeScript с комментариями для ключевых частей
- README с инструкцией по установке, запуску, сборке и деплою проекта

## 8. Дополнительные требования

- Все секции и повторяющиеся элементы реализовать как отдельные компоненты с пропсами для переиспользования
- Статические данные (списки, преимущества, вертикали) вынести в отдельные файлы для удобства редактирования
- Не использовать сторонние UI-библиотеки (например, MUI, Ant Design), кроме Tailwind CSS
- Не допускать дублирования кода и стилей
- Все кнопки "Talk to an expert" должны вести на страницу контактов

## 9. Этапы реализации

1. Анализ лендинга, сбор всех необходимых материалов (тексты, изображения, иконки)
2. Инициализация проекта Next.js с TypeScript и Tailwind CSS
3. Разработка структуры страниц и компонентов
4. Верстка и стилизация секций
5. Реализация функциональности (навигация, формы, SEO, оптимизация)
6. Тестирование на устройствах и браузерах
7. Подготовка и передача исходников с документацией

## 10. Стандарты качества и автоматизация

- **ESLint & Prettier**: настроить единые конфигурации, добавить npm-скрипты `lint`, `format`. Ошибки линтера должны приводить к неудачной сборке.
- **Husky (опц.)**: pre-commit хук `npm run lint && npm run format`.
- **CI/CD**: GitHub Actions (или GitLab CI) с этапами `install → lint → build → lighthouse → deploy`. Деплой — Vercel/Netlify; предварительный просмотр создаётся для каждого Pull Request.
- **Бюджет производительности**: Lighthouse CI, порог — Performance ≥ 90, LCP ≤ 2.5 c, CLS ≤ 0.1.
- **Базовая доступность (a11y)**: обязательные `alt`, семантические теги (`header`, `nav`, `main`, `footer`). В CI запускать axe-linter.
- **SEO-метаданные**: генерация `sitemap.xml`, `robots.txt`, корректные Open Graph / Twitter Cards через `next-seo` или вручную в `Head`.

## 11. Блог

> Блок является **опциональным** и добавляется при необходимости публикации новостей/статей.

1. **Структура страниц**  
   - `/blog/index.tsx` — список статей с пагинацией.  
   - `/blog/[slug].tsx` — страница отдельной статьи (динамический роут, SSG).

2. **Хранение контента**  
   - Markdown/MDX-файлы в `/content/blog`, один файл — одна статья.  
   - Метаданные (title, description, date, tags) — в *front-matter*.

3. **Генерация страниц**  
   - `getStaticPaths` + `getStaticProps` для статей.  
   - При росте количества постов использовать ISR или пагинацию.

4. **SEO**  
   - Уникальные `title`, `description`, OG/Twitter Cards на основе front-matter.  
   - Генерация `rss.xml` и добавление статей в `sitemap.xml`.

5. **Компоненты**  
   - `BlogCard.tsx` — превью статьи.  
   - `MarkdownRenderer.tsx` / MDX Remote для рендера контента.  
   - `Tag.tsx` / `TagList.tsx` — фильтрация по тегам (опц.).

6. **Стилизация**  
   - Плагин `@tailwindcss/typography` для класса `prose`.

7. **Оптимизация изображений**  
   - Автоматическая замена `<img>` на `next/image` через remark/rehype-плагин.

8. **Тестирование**  
   - Snapshot-тесты списка и рендера статьи.

9. **CI/CD**  
   - Проверка генерации RSS и ссылок в sitemap в pipeline.

**Примечание:** Использовать только TypeScript, Tailwind CSS и Next.js. Все решения и структура кода должны быть максимально прозрачными и удобными для дальнейшей поддержки и развития.
ссылка на видео https://www.youtube.com/watch?v=mReZr_e70OA